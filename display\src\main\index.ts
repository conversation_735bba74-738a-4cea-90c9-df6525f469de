import { app, shell, BrowserWindow, ipc<PERSON><PERSON>, <PERSON>u } from "electron";
import { join } from "path";
import { electronApp, optimizer, is } from "@electron-toolkit/utils";
import icon from "../../resources/icon.png?asset";
// import { embeddedAssetManager } from "./embedded-asset-manager";

// Store instance will be initialized asynchronously
let authStore: any = null;

// Initialize Electron Store asynchronously
const initializeStore = async () => {
  try {
    const ElectronStore = await import("electron-store");
    const StoreClass = ElectronStore.default;
    authStore = new StoreClass({
      name: "auth",
      watch: true,
    });
    // Electron Store initialized successfully
  } catch (error) {
    console.error("❌ Failed to initialize Electron Store:", error);
  }
};

function createWindow(): void {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    show: false,
    fullscreen: true,
    autoHideMenuBar: true,
    kiosk: true, // Kiosk mode - prevents exiting fullscreen
    alwaysOnTop: true, // Keep window always on top
    skipTaskbar: true, // Hide from taskbar
    frame: false, // Remove window frame completely
    resizable: false, // Prevent resizing
    minimizable: false, // Prevent minimizing
    maximizable: false, // Prevent maximizing
    closable: false, // Prevent closing (optional - remove if you want close button)
    ...(process.platform === "linux" ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, "../preload/index.js"),
      sandbox: false,
      webSecurity: true,
    },
  });

  mainWindow.on("ready-to-show", () => {
    mainWindow.show();
    mainWindow.setFullScreen(true); // Ensure fullscreen
    mainWindow.setMenuBarVisibility(false); // Hide menu bar completely

    // Completely disable application menu
    Menu.setApplicationMenu(null);
  });

  // Prevent exiting fullscreen
  mainWindow.on("leave-full-screen", () => {
    mainWindow.setFullScreen(true);
  });

  // Prevent window from being minimized
  mainWindow.on("minimize", () => {
    mainWindow.restore();
  });

  // Prevent window from being closed (optional)
  // mainWindow.on("close", (event) => {
  //   event.preventDefault(); // Remove this if you want to allow closing
  // });

  // Disable all keyboard shortcuts and menu access
  mainWindow.webContents.on("before-input-event", (event, input) => {
    // Disable F11 (fullscreen toggle)
    if (input.key === "F11") {
      event.preventDefault();
      return;
    }

    // Disable Alt key (menu access)
    if (input.key === "Alt") {
      event.preventDefault();
      return;
    }

    // Disable Alt+F4 (close window)
    // if (input.alt && input.key === "F4") {
    //   event.preventDefault();
    //   return;
    // }

    // Disable Ctrl+W (close tab/window)
    if (input.control && input.key === "w") {
      event.preventDefault();
      return;
    }

    // Disable Ctrl+Q (quit application)
    if (input.control && input.key === "q") {
      event.preventDefault();
      return;
    }

    // Disable Ctrl+R (reload application)
    if (input.control && input.key === "r") {
      event.preventDefault();
      return;
    }

    // Disable Escape key (exit fullscreen)
    if (input.key === "Escape") {
      event.preventDefault();
      return;
    }

    // Disable Windows key
    if (input.key === "Meta") {
      event.preventDefault();
      return;
    }

    // Disable Ctrl+Alt+Del equivalent shortcuts
    if (input.control && input.alt) {
      event.preventDefault();
      return;
    }
  });

  // Disable context menu (right-click menu)
  mainWindow.webContents.on("context-menu", (event) => {
    event.preventDefault();
  });

  // Set Content Security Policy to allow embedded-asset protocol
  mainWindow.webContents.session.webRequest.onHeadersReceived(
    (details, callback) => {
      callback({
        responseHeaders: {
          ...details.responseHeaders,
          "Content-Security-Policy": [
            "default-src 'self' 'unsafe-inline' 'unsafe-eval' embedded-asset: data: blob:; " +
              "img-src 'self' 'unsafe-inline' embedded-asset: data: blob:; " +
              "media-src 'self' 'unsafe-inline' embedded-asset: data: blob:; " +
              "font-src 'self' 'unsafe-inline' embedded-asset: data: blob:; " +
              "style-src 'self' 'unsafe-inline' embedded-asset: data: blob:; " +
              "script-src 'self' 'unsafe-inline' 'unsafe-eval' embedded-asset: data: blob:; " +
              "connect-src 'self' ws: wss: http: https: embedded-asset: data: blob:;",
          ],
        },
      });
    },
  );

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url);
    return { action: "deny" };
  });

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env["ELECTRON_RENDERER_URL"]) {
    mainWindow.loadURL(process.env["ELECTRON_RENDERER_URL"]);
  } else {
    mainWindow.loadFile(join(__dirname, "../renderer/index.html"));
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => {
  // Set app user model id for windows
  electronApp.setAppUserModelId("com.electron");

  // Initialize Electron Store
  await initializeStore();

  // Set up IPC handlers for auth store
  ipcMain.handle("auth-store-set", (_event, key: string, value: any) => {
    if (!authStore) {
      throw new Error("Auth store not initialized");
    }
    return authStore.set(key, value);
  });

  ipcMain.handle("auth-store-get", (_event, key: string) => {
    if (!authStore) {
      throw new Error("Auth store not initialized");
    }
    return authStore.get(key);
  });

  ipcMain.handle("auth-store-has", (_event, key: string) => {
    if (!authStore) {
      throw new Error("Auth store not initialized");
    }
    return authStore.has(key);
  });

  ipcMain.handle("auth-store-delete", (_event, key: string) => {
    if (!authStore) {
      throw new Error("Auth store not initialized");
    }
    return authStore.delete(key);
  });

  // // Initialize embedded asset manager
  // if (embeddedAssetManager.initialize()) {
  //   embeddedAssetManager.registerProtocol();
  // } else {
  // }

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on("browser-window-created", (_, window) => {
    optimizer.watchWindowShortcuts(window);
  });

  // IPC test
  ipcMain.on("ping", () => console.log("pong"));

  createWindow();

  app.on("activate", function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
});
