{"name": "react-error-boundary", "version": "5.0.0", "description": "Simple reusable React error boundary component", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/bvaughn/react-error-boundary"}, "packageManager": "pnpm@9.6.0", "main": "dist/react-error-boundary.cjs.js", "module": "dist/react-error-boundary.esm.js", "exports": {".": {"types": {"import": "./dist/react-error-boundary.cjs.mjs", "default": "./dist/react-error-boundary.cjs.js"}, "development": {"module": "./dist/react-error-boundary.development.esm.js", "import": "./dist/react-error-boundary.development.cjs.mjs", "default": "./dist/react-error-boundary.development.cjs.js"}, "module": "./dist/react-error-boundary.esm.js", "import": "./dist/react-error-boundary.cjs.mjs", "default": "./dist/react-error-boundary.cjs.js"}, "./package.json": "./package.json"}, "imports": {"#is-development": {"development": "./src/env-conditions/development.ts", "default": "./src/env-conditions/production.ts"}}, "types": "dist/react-error-boundary.cjs.d.ts", "files": ["dist"], "sideEffects": false, "scripts": {"clear": "pnpm clear:builds & pnpm clear:node_modules", "clear:builds": "rimraf ./dist", "clear:node_modules": "rimraf ./node_modules", "prerelease": "preconstruct build", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "jest", "test:watch": "jest --watch", "typescript": "tsc --noEmit", "typescript:watch": "tsc --noEmit --watch"}, "dependencies": {"@babel/runtime": "^7.12.5"}, "devDependencies": {"@babel/preset-env": "^7.22.5", "@babel/preset-typescript": "^7.21.5", "@preconstruct/cli": "^2.8.1", "@types/assert": "^1.5.10", "@types/jest": "^26.0.15", "@types/react": "^18.3.17", "@types/react-dom": "^18", "assert": "^2.0.0", "eslint": "^9.13.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "globals": "^15.11.0", "jest": "^29.4.3", "jest-environment-jsdom": "^29.4.3", "prettier": "^3.0.1", "react": "^18", "react-dom": "^18", "rimraf": "^6.0.1", "ts-jest": "^29.0.5", "typescript": "^5.1.6", "typescript-eslint": "^8.18.0"}, "peerDependencies": {"react": ">=16.13.1"}, "preconstruct": {"exports": {"importConditionDefaultExport": "default"}, "___experimentalFlags_WILL_CHANGE_IN_PATCH": {"importsConditions": true}}}