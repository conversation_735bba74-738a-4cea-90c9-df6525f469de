Stack trace:
Frame         Function      Args
0007FFFFAEF0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFAEF0, 0007FFFF9DF0) msys-2.0.dll+0x1FE8E
0007FFFFAEF0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFB1C8) msys-2.0.dll+0x67F9
0007FFFFAEF0  000210046832 (000210286019, 0007FFFFADA8, 0007FFFFAEF0, 000000000000) msys-2.0.dll+0x6832
0007FFFFAEF0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFAEF0  000210068E24 (0007FFFFAF00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFB1D0  00021006A225 (0007FFFFAF00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE19250000 ntdll.dll
7FFE17E30000 KERNEL32.DLL
7FFE16BC0000 KERNELBASE.dll
7FFE189E0000 USER32.dll
7FFE168F0000 win32u.dll
7FFE17B30000 GDI32.dll
000210040000 msys-2.0.dll
7FFE169D0000 gdi32full.dll
7FFE16EC0000 msvcp_win.dll
7FFE16F60000 ucrtbase.dll
7FFE17830000 advapi32.dll
7FFE17D90000 msvcrt.dll
7FFE17FA0000 sechost.dll
7FFE178E0000 RPCRT4.dll
7FFE17060000 bcrypt.dll
7FFE161B0000 CRYPTBASE.DLL
7FFE171F0000 bcryptPrimitives.dll
7FFE17C80000 IMM32.DLL
