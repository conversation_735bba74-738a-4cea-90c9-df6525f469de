Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFBBB0, 0007FFFFAAB0) msys-2.0.dll+0x1FE8E
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 00021005A0D2, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (0002102860C8, 0007FFFFBA68, 0007FFFFBBB0, 0002102684E0) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBBB0  000210068EFF (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28EFF
0007FFFFBE90  00021006A225 (000000004000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
0007FFFFBE90  00021006A4A9 (000000000000, 0007FFFFBF38, 0007FFFFC114, 0000FFFFFFFF) msys-2.0.dll+0x2A4A9
0007FFFFBE90  000210193F2B (000000000000, 0007FFFFBF38, 0007FFFFC114, 0000FFFFFFFF) msys-2.0.dll+0x153F2B
0007FFFFBE90  00010042DB65 (000A00000004, 0007FFFFC124, 00010040DD62, 000000000010) bash.exe+0x2DB65
0000FFFFFFFF  00010043C4F8 (0000000000C2, 000A00000000, 000A001158E0, 000A00087EF0) bash.exe+0x3C4F8
000000000070  00010043E6BE (000A0017E520, 000A00000001, 000210178DD2, 0007FFFFC120) bash.exe+0x3E6BE
000000000070  000100441B06 (000700000001, 000A00000000, 0007FFFFC210, 000000000000) bash.exe+0x41B06
000000000070  000100441D36 (000200000000, 000A00000000, 000000000000, 000000000000) bash.exe+0x41D36
000A0004CA90  000100444B1F (0000000000A9, 000A000518B0, 000000000030, 0000000000A9) bash.exe+0x44B1F
000A0004CA90  0001004151FF (000A000E9650, FFFFFFFFFFFC9644, 8080808080808080, 0001004F6EF7) bash.exe+0x151FF
000A000E9650  00010041561E (000000000001, 000000000001, 000210178DD2, 000200000000) bash.exe+0x1561E
0000FFFFFFFF  00010041792C (000A000AC650, 000000000000, 000A000AC650, 000A001278C0) bash.exe+0x1792C
0000000000A9  00010041AC6A (000A0017E520, 000210268720, 000100620700, 000000000001) bash.exe+0x1AC6A
0000000000A9  0001004180FB (0007FFFFC610, 000A00029BD0, 0002100AC638, 000A000FBFC0) bash.exe+0x180FB
000000000001  00010046F3A5 (000A0004BBB0, 000000000414, 0000CE000000, 0000000000BE) bash.exe+0x6F3A5
000A00029C80  00010046E127 (000000000009, 000A000234F0, 000000000000, 0007FFFFCBEF) bash.exe+0x6E127
000000000000  00010046E2D5 (0002102ADAC0, 0002102686E0, 000000000000, 000000000005) bash.exe+0x6E2D5
000000000000  0001004EA48C (000A00002870, 000A00000160, 0002100C96E9, 000000000000) bash.exe+0xEA48C
0007FFFFCD30  000210047F01 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x7F01
000000000000  000210045AC3 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x5AC3
0007FFFFFFF0  000210045B74 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x5B74
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE19250000 ntdll.dll
7FFE17E30000 KERNEL32.DLL
7FFE16BC0000 KERNELBASE.dll
7FFE189E0000 USER32.dll
7FFE168F0000 win32u.dll
7FFE17B30000 GDI32.dll
7FFE169D0000 gdi32full.dll
7FFE16EC0000 msvcp_win.dll
7FFE16F60000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE17830000 advapi32.dll
7FFE17D90000 msvcrt.dll
7FFE17FA0000 sechost.dll
7FFE178E0000 RPCRT4.dll
7FFE17060000 bcrypt.dll
7FFE161B0000 CRYPTBASE.DLL
7FFE171F0000 bcryptPrimitives.dll
7FFE17C80000 IMM32.DLL
