import "../../game.css";

import draw9Img from "../../assets/images/9draw.png";
import betsClosedImg from "../../assets/images/9betsclosed.png";
import appLogoImg from "../../assets/images/app-logo.png";

import placeHolderImg from "../../assets/images/placeholder.png";
import introvideoImg from "../../assets/images/introvideoImg.jpg";

import introVideo from "../../assets/videos/z4.mp4";
import introSound from "../../assets/sounds/1intro.mp3";
import countdownSound from "../../assets/sounds/countdownAudio.mp3";

const outcomeVideoModules = import.meta.glob("../../assets/videos/*.mp4", {
  import: "default",
  eager: true,
  query: "?url",
});
const numberImageModules = import.meta.glob(
  "../../assets/images/numbers/*.png",
  { eager: true, query: "?url", import: "default" },
);

function getOutcomeVideoUrl(outcomeName: string | number) {
  const path = `../../assets/videos/${outcomeName}.mp4`;
  return outcomeVideoModules[path] as string | undefined;
}

function getNumberImageUrl(imageName: string | number) {
  const path = `../../assets/images/numbers/${imageName}.png`;
  return numberImageModules[path] as string | undefined;
}

import { useEffect, useMemo, useRef, useState, useCallback } from "react";
import { createFileRoute } from "@tanstack/react-router";

import "./-components/history-styles.css";

import { useQuery } from "@tanstack/react-query";
import { apiClient } from "../../apiClient";
import { useSocketIO } from "../../contexts/SocketIOContext";
import { useVideoPreloader } from "../../hooks/useVideoPreloader";

import { LeftSideBar } from "./-components/LeftSide";

// Define PRIZE_STRUCTURE at the module level
const PRIZE_STRUCTURE = {
  1: { 1: 3.8, 0: 0 },
  2: { 2: 15, 1: 0, 0: 0 },
  3: { 3: 35, 2: 3, 1: 0, 0: 0 },
  4: { 4: 100, 3: 8, 2: 1, 1: 0, 0: 0 },
  5: { 5: 300, 4: 15, 3: 3, 2: 1, 1: 0, 0: 0 },
  6: { 6: 1800, 5: 70, 4: 10, 3: 1, 2: 0, 1: 0, 0: 0 },
  7: { 7: 2150, 6: 120, 5: 12, 4: 6, 3: 1, 2: 0, 1: 0, 0: 0 },
  8: { 8: 3000, 7: 600, 6: 68, 5: 8, 4: 4, 3: 0, 2: 0, 1: 0, 0: 0 },
  9: { 9: 4200, 8: 1800, 7: 120, 6: 18, 5: 6, 4: 3, 3: 0, 2: 0, 1: 0, 0: 0 },
  10: {
    10: 5000,
    9: 2500,
    8: 400,
    7: 40,
    6: 12,
    5: 4,
    4: 2,
    3: 0,
    2: 0,
    1: 0,
    0: 0,
  },
};

// Define pick level time thresholds in seconds (updated for 4-minute countdown)
// Each pick level can now have multiple display times during the countdown
const PICK_LEVEL_TIMES = {
  10: [205, 147, 94, 40], // 3 min 0 sec, 2 min 27 sec, 1 min 37 sec, 34 sec
  9: [201, 143, 90, 36], // 2 min 55 sec, 2 min 22 sec, 1 min 32 sec, 29 sec
  8: [197, 139, 85, 32], // 2 min 50 sec, 2 min 17 sec, 1 min 27 sec, 24 sec
  7: [192, 136, 82, 29], // 2 min 45 sec, 2 min 12 sec, 1 min 22 sec, 19 sec
  6: [187, 132, 79, 25], // 2 min 40 sec, 2 min 7 sec, 1 min 17 sec, 14 sec
  5: [182, 129, 75, 22], // 2 min 35 sec, 2 min 2 sec, 1 min 12 sec, 9 sec
  4: [177, 125, 72, 19], // 2 min 30 sec, 1 min 57 sec, 1 min 7 sec, 4 sec
  3: [172, 122, 69, 15], // 2 min 25 sec, 1 min 52 sec, 1 min 2 sec
  2: [167, 119, 65, 12], // 2 min 20 sec, 1 min 47 sec, 57 sec
  1: [162, 115, 62, 9], // 2 min 15 sec, 1 min 42 sec, 52 sec
};

const TIMING = {
  INTRO_DURATION_MS: 5208.33,
  DRAW_DELAY_MS: 0,
  NUMBER_VIDEO_DURATION_MS: 2500,
  get TOTAL_DRAW_DURATION_MS() {
    return this.INTRO_DURATION_MS + 20 * this.NUMBER_VIDEO_DURATION_MS;
  },
};

type TGameStatus = {
  eventNumber?: number;
  status: "BETTING_OPEN" | "BETTING_CLOSED";
  endTime: Date;
};

type TGameHistory = {
  _id: string;
  eventNumber: number;
  drawnNumbers: number[];
};

export const Route = createFileRoute("/(game)/")({
  component: RouteComponent,
});

function RouteComponent() {
  const { socket, isServerDown, detectPhaseTransition } = useSocketIO();
  const {
    status: videoPreloadStatus,
    preloadVideos,
    isVideoReady,
  } = useVideoPreloader();

  const MAX_HISTORY_ENTRIES = 10;

  const storeLocalGameHistory = (history: TGameHistory[]) => {
    sessionStorage.setItem("localGameHistory2", JSON.stringify(history));
  };

  const retrieveLocalGameHistory = (): TGameHistory[] => {
    const history = sessionStorage.getItem("localGameHistory2");
    return history ? JSON.parse(history) : [];
  };

  const [pickLevel, setPickLevel] = useState<number>(10);

  const displayablePrizes = useMemo(() => {
    const prizesForPick =
      PRIZE_STRUCTURE[pickLevel as keyof typeof PRIZE_STRUCTURE];
    if (!prizesForPick) return [];

    return Object.entries(prizesForPick)
      .map(([hits, win]) => ({ hits: parseInt(hits), win: win as number }))
      .filter((p) => p.win > 0) // Only show entries with actual prize money
      .sort((a, b) => b.hits - a.hits); // Display highest hits first
  }, [pickLevel]);

  const videoRef = useRef<HTMLVideoElement>(null);
  const introVideoRef = useRef<HTMLVideoElement>(null);

  const [gameHistory, setGameHistory] = useState<TGameHistory[]>(() => {
    return retrieveLocalGameHistory();
  });
  const [showGameHistory, setShowGameHistory] = useState(false);

  const [gameStatus, setGameStaus] = useState<TGameStatus | undefined>(
    undefined,
  );
  const [countdown, setCountdown] = useState<number | undefined>(undefined);

  // Update pick level based on countdown time
  useEffect(() => {
    if (countdown === undefined) return;

    let foundPickLevel: number | null = null;

    for (let level = 10; level >= 1; level--) {
      const times = PICK_LEVEL_TIMES[level as keyof typeof PICK_LEVEL_TIMES];
      if (times && times.includes(countdown)) {
        foundPickLevel = level;
        break;
      }
    }

    if (foundPickLevel !== null && foundPickLevel !== pickLevel) {
      setPickLevel(foundPickLevel);
    }
  }, [countdown, pickLevel]);

  const [allOutcomes, setAllOutcomes] = useState<number[]>([]);
  const [eventNumber, setEventNumber] = useState<number>();
  const [offlineroundNumber, setOfflineroundNumber] = useState<
    number | undefined
  >();
  // Track when we first enter offline mode
  const [wasServerDown, setWasServerDown] = useState(false);

  const [outcomes, setOutcomes] = useState<number[]>([]);
  const [drawnNumber, setDrawnNumber] = useState<number>();
  const [delayedDrawnNumber, setDelayedDrawnNumber] = useState<
    number | undefined
  >(undefined);

  const [showIntroVideo, setShowIntroVideo] = useState(false);
  const [drawing, setDrawing] = useState(false);

  const [noDataTimer, setNoDataTimer] = useState<NodeJS.Timeout | null>(null);

  const [bettingClosedCheckTimer, setBettingClosedCheckTimer] =
    useState<NodeJS.Timeout | null>(null);

  const [lastDataReceivedTime, setLastDataReceivedTime] = useState(Date.now());

  const introAudioRef = useRef<HTMLAudioElement>(null);
  const countdownAudio = useRef<HTMLAudioElement>(null);
  // const { data } = useQuery({
  //   queryKey: ["gameStatus"],
  //   queryFn: () => apiClient("events/status").then((res: any) => res.data),
  // });
  const lastGame = useQuery({
    queryKey: ["lastGameStatus"],
    queryFn: () => apiClient("events/last-game").then((res: any) => res.data),
  });

  useEffect(() => {
    // if (data) {
    //   setGameStaus(data);
    //   convertDateToCountDown(data.endTime);
    // }

    // Run drawing if the game is still supposed to be drawwing
    if (lastGame.data) {
      console.log(lastGame.data);
      const {
        drawnNumbers,
        drawTime,
        status,
        eventNumber: lastroundNumber,
      } = lastGame.data;
      if (status === "COMPLETED" && drawnNumbers?.length && drawTime) {
        const drawTimestamp = new Date(drawTime).getTime();
        const now = Date.now();
        const elapsedMs = now - drawTimestamp;

        setAllOutcomes(drawnNumbers);
        setEventNumber(lastroundNumber);

        const totalDrawDuration =
          TIMING.INTRO_DURATION_MS +
          drawnNumbers.length * TIMING.NUMBER_VIDEO_DURATION_MS;

        if (elapsedMs < totalDrawDuration) {
          setDrawing(true);

          if (elapsedMs < TIMING.INTRO_DURATION_MS) {
            setShowIntroVideo(true);
            setOutcomes([]);
            setDrawnNumber(undefined);

            if (drawnNumbers) {
              const newGameHistoryEntry: TGameHistory = {
                _id: Date.now().toString(), // Generate a unique ID
                eventNumber: lastroundNumber,
                drawnNumbers: drawnNumbers,
              };

              setGameHistory((prevHistory) => {
                const updatedHistory = [
                  newGameHistoryEntry,
                  ...prevHistory,
                ].slice(0, MAX_HISTORY_ENTRIES);

                storeLocalGameHistory(updatedHistory);

                return updatedHistory;
              });
            }

            const introPosition = Math.max(0, elapsedMs / 1000);

            if (introVideoRef.current) {
              introVideoRef.current.setAttribute(
                "data-start-time",
                introPosition.toString(),
              );
            }

            if (introAudioRef.current) {
              introAudioRef.current.currentTime = 0;
              introAudioRef.current.play();
            }
          } else {
            setShowIntroVideo(false);
            if (drawnNumbers) {
              const newGameHistoryEntry: TGameHistory = {
                _id: Date.now().toString(), // Generate a unique ID
                eventNumber: lastroundNumber,
                drawnNumbers: drawnNumbers,
              };

              setGameHistory((prevHistory) => {
                const updatedHistory = [
                  newGameHistoryEntry,
                  ...prevHistory,
                ].slice(0, MAX_HISTORY_ENTRIES);

                storeLocalGameHistory(updatedHistory);

                return updatedHistory;
              });
            }

            const timeIntoNumberDrawing = elapsedMs - TIMING.INTRO_DURATION_MS;
            const currentNumberIndex = Math.min(
              Math.floor(
                timeIntoNumberDrawing / TIMING.NUMBER_VIDEO_DURATION_MS,
              ),
              drawnNumbers.length - 1,
            );

            setOutcomes(drawnNumbers.slice(0, currentNumberIndex));

            setDrawnNumber(drawnNumbers[currentNumberIndex]);
            setTimeout(() => {
              setOutcomes(drawnNumbers.slice(0, currentNumberIndex + 1));
            }, TIMING.NUMBER_VIDEO_DURATION_MS / 2);

            if (videoRef.current) {
              const videoPosition =
                timeIntoNumberDrawing % TIMING.NUMBER_VIDEO_DURATION_MS;
              videoRef.current.currentTime = Math.max(0, videoPosition / 1000);
              videoRef.current.play();
            }
          }
        } else {
          setDrawing(false);
          setShowIntroVideo(false);
          setOutcomes(drawnNumbers);
          setDrawnNumber(undefined);
        }
      } else if (status !== "COMPLETED") {
        setDrawing(false);
        setShowIntroVideo(false);
        setOutcomes([]);
        setAllOutcomes([]);
        setDrawnNumber(undefined);
        if (lastGame.data.eventNumber) {
          setEventNumber(lastGame.data.eventNumber);
        }
      }
    }
  }, [lastGame.data]);

  // Simplified offline mode to handle server disconnections
  useEffect(() => {
    if (isServerDown) {
      if (!wasServerDown) {
        setWasServerDown(true);
        let initialOfflineRound = eventNumber && eventNumber + 1;
        setOfflineroundNumber(initialOfflineRound);
      }

      if (countdown === 0) {
        setGameStaus({
          status: "BETTING_CLOSED",
          endTime: new Date(),
        });

        setTimeout(() => {
          const nextRound = offlineroundNumber! + 1;
          setOfflineroundNumber(nextRound);

          setGameStaus({
            status: "BETTING_OPEN",
            endTime: new Date(Date.now() + 240 * 1000),
          });

          setCountdown(240);
        }, 5000);
      }
    } else {
      setWasServerDown(false);
    }
  }, [isServerDown, countdown, eventNumber, offlineroundNumber]);

  useEffect(() => {
    return () => {
      if (noDataTimer) {
        clearTimeout(noDataTimer);
      }
      if (bettingClosedCheckTimer) {
        clearTimeout(bettingClosedCheckTimer);
      }
    };
  }, [noDataTimer, countdown, socket, bettingClosedCheckTimer]);

  useEffect(() => {
    if (!socket) return;

    if (countdown === 0 || countdown === undefined) {
      socket.emit("getEventDetails");
    }

    const handleGameStatus = (res: TGameStatus) => {
      if (res) {
        setGameStaus(res);
        convertDateToCountDown(res.endTime);

        setLastDataReceivedTime(Date.now());

        // If we receive game status data from the server after being offline,
        // reset wasServerDown to false to indicate we're back online
        if (wasServerDown) {
          setWasServerDown(false);

          // If we have a eventNumber from the server, update our offlineroundNumber to match
          if (res.eventNumber) {
            setOfflineroundNumber(res.eventNumber);
          }
        }

        setGameStaus(res);
      }
    };

    const handleDrawnNumbers = (res: {
      drawnNumbers: number[];
      eventNumber: number;
    }) => {
      if (res) {
        setLastDataReceivedTime(Date.now());

        // Add the current game to the history array
        const newGameHistoryEntry: TGameHistory = {
          _id: Date.now().toString(), // Generate a unique ID
          eventNumber: res.eventNumber,
          drawnNumbers: res.drawnNumbers,
        };

        setGameHistory((prevHistory) => {
          const updatedHistory = [newGameHistoryEntry, ...prevHistory].slice(
            0,
            MAX_HISTORY_ENTRIES,
          );

          storeLocalGameHistory(updatedHistory);

          return updatedHistory;
        });

        setAllOutcomes(res.drawnNumbers);

        setEventNumber(res.eventNumber);
        // reset the offline round number to match the server's round number
        setOfflineroundNumber(res.eventNumber);
        // Reset wasServerDown when we get new data from the server
        setWasServerDown(false);
        setOutcomes([]);
        setDrawnNumber(undefined);
        setShowIntroVideo(true);

        if (introAudioRef.current) {
          introAudioRef.current.pause();
          introAudioRef.current.currentTime = 0;
          introAudioRef.current.play();
        }

        if (introVideoRef.current) {
          introVideoRef.current.currentTime = 0;
          introVideoRef.current.play();
        }
      }
    };

    socket.on("gameStatus", handleGameStatus);
    socket.on("drawnNumbers", handleDrawnNumbers);

    return () => {
      socket.off("gameStatus");
      socket.off("drawnNumbers");

      if (noDataTimer) {
        clearTimeout(noDataTimer);
      }
      if (bettingClosedCheckTimer) {
        clearTimeout(bettingClosedCheckTimer);
      }
    };
  }, [socket, noDataTimer, bettingClosedCheckTimer]);

  useEffect(() => {
    if (!gameStatus?.endTime) {
      setCountdown(0);
      return;
    }

    let intervalTimer: NodeJS.Timeout | null = null;

    const startTimer = () => {
      intervalTimer = setInterval(() => {
        if (!gameStatus?.endTime) {
          if (intervalTimer) clearInterval(intervalTimer);
          setCountdown(0);
          return;
        }

        const endTimestamp = new Date(gameStatus.endTime).getTime();
        const remaining = Math.max(
          0,
          Math.round((endTimestamp - Date.now()) / 1000),
        );

        // Update the countdown
        setCountdown(remaining);

        // Detect phase transitions for the socket context
        if (detectPhaseTransition) {
          detectPhaseTransition(gameStatus.status, remaining);
        }

        // Update countdown value
      }, 1000);
    };

    startTimer();

    return () => {
      if (intervalTimer) {
        clearInterval(intervalTimer);
      }
    };
  }, [
    gameStatus?.endTime,
    gameStatus?.status,
    lastDataReceivedTime,
    drawing,
    showIntroVideo,
    isServerDown,
  ]);

  // Hanlde video playback when reloaded
  useEffect(() => {
    const introVideo = introVideoRef.current;
    const numberVideo = videoRef.current;

    // Mark videos as externally controlled to prevent autoplay conflicts
    if (introVideo) {
      introVideo.setAttribute("data-external-control", "true");
    }
    if (numberVideo) {
      numberVideo.setAttribute("data-external-control", "true");
    }

    if (showIntroVideo && introVideo) {
      console.log("🎬 Effect: Playing intro video");

      // Pause number video first
      if (numberVideo) {
        numberVideo.pause();
        numberVideo.currentTime = 0;
      }

      // Wait for video to be ready before playing
      const playIntroVideo = () => {
        if (introVideo.readyState >= 2) {
          // HAVE_CURRENT_DATA
          introVideo.currentTime = 0;
          introVideo
            .play()
            .then(() => console.log("✅ Intro video started successfully"))
            .catch((e) => console.error("❌ Intro video play failed:", e));
        } else {
          // Wait for video to be ready
          const handleCanPlay = () => {
            introVideo.currentTime = 0;
            introVideo
              .play()
              .then(() => console.log("✅ Intro video started successfully"))
              .catch((e) => console.error("❌ Intro video play failed:", e));
            introVideo.removeEventListener("canplay", handleCanPlay);
          };
          introVideo.addEventListener("canplay", handleCanPlay);
        }
      };

      playIntroVideo();
    } else if (
      drawing &&
      !showIntroVideo &&
      delayedDrawnNumber &&
      numberVideo
    ) {
      // Pause intro video first
      if (introVideo) {
        introVideo.pause();
        introVideo.currentTime = 0;
      }

      // For the persistent video element, we need to wait for the new src to load
      const handleCanPlay = () => {
        numberVideo.currentTime = 0;
        numberVideo
          .play()
          .then(() =>
            console.log(
              `✅ Number video ${delayedDrawnNumber} started successfully`,
            ),
          )
          .catch((e) =>
            console.error(
              `❌ Number video ${delayedDrawnNumber} play failed:`,
              e,
            ),
          );
        numberVideo.removeEventListener("canplay", handleCanPlay);
      };

      // Add event listener for when the new video can play
      numberVideo.addEventListener("canplay", handleCanPlay);

      // If the video is already loaded and ready, play immediately
      if (numberVideo.readyState >= 3) {
        // HAVE_FUTURE_DATA or higher
        handleCanPlay();
      }
    } else {
      // Pause all videos when not needed
      if (introVideo) {
        console.log("⏸️ Pausing intro video");
        introVideo.pause();
        introVideo.currentTime = 0;
      }
      if (numberVideo) {
        console.log("⏸️ Pausing number video");
        numberVideo.pause();
        numberVideo.currentTime = 0;
      }
    }
  }, [showIntroVideo, drawing, delayedDrawnNumber]);

  // Handle intro audio playback
  useEffect(() => {
    const introAudio = introAudioRef.current;
    if (!drawing && !showIntroVideo && !showGameHistory && introAudio) {
      console.log("Playing intro audio");
      introAudio.currentTime = 0;
      introAudio
        .play()
        .catch((e) => console.error("Intro audio play failed:", e));
    } else if (introAudio) {
      introAudio.pause();
    }
  }, [drawing, showIntroVideo, showGameHistory]);

  // Handle countdown audio playback
  useEffect(() => {
    const countdownAudioElement = countdownAudio.current;
    if (countdown && countdown <= 10 && countdownAudioElement) {
      console.log("Playing countdown audio");
      countdownAudioElement.currentTime = 0;
      countdownAudioElement
        .play()
        .catch((e) => console.error("Countdown audio play failed:", e));
    } else if (countdownAudioElement) {
      countdownAudioElement.pause();
    }
  }, [countdown]);

  useEffect(() => {
    if (!drawing || !allOutcomes.length) {
      return;
    }

    let drawTimer: NodeJS.Timeout | null = null;
    let currentNumberIndex = -1;

    const drawNextNumber = () => {
      currentNumberIndex++;

      if (currentNumberIndex < allOutcomes.length) {
        const nextNumber = allOutcomes[currentNumberIndex];
        console.log(`Drawing number ${currentNumberIndex + 1}: ${nextNumber}`);

        setDrawnNumber(nextNumber);

        const revealDelay = TIMING.NUMBER_VIDEO_DURATION_MS / 2;
        setTimeout(() => {
          setOutcomes((prevOutcomes) => {
            if (drawing && !prevOutcomes.includes(nextNumber)) {
              return [...prevOutcomes, nextNumber];
            }
            return prevOutcomes;
          });
        }, revealDelay);

        drawTimer = setTimeout(drawNextNumber, TIMING.NUMBER_VIDEO_DURATION_MS);
      } else {
        console.log("Drawing sequence complete.");
        setShowIntroVideo(false);
        setTimeout(() => {
          setDrawing(false);
          setShowGameHistory(true);
          setDrawnNumber(undefined);
        }, 5000);

        setTimeout(() => {
          setShowGameHistory(false);
        }, 23000);
      }
    };

    if (showIntroVideo) {
      console.log("Waiting for intro video to complete...");
      drawTimer = setTimeout(() => {
        if (drawing) {
          console.log("Intro finished, starting number draw.");
          setShowIntroVideo(false);
          drawNextNumber();
        }
      }, TIMING.INTRO_DURATION_MS);
    } else {
      const lastRevealedNumber =
        outcomes.length > 0 ? outcomes[outcomes.length - 1] : undefined;
      currentNumberIndex = lastRevealedNumber
        ? allOutcomes.indexOf(lastRevealedNumber)
        : -1;

      console.log(
        `Drawing effect started without intro. Resuming after index: ${currentNumberIndex}`,
      );

      if (drawnNumber) {
        const drawnNumberIndex = allOutcomes.indexOf(drawnNumber);
        if (drawnNumberIndex !== -1) {
          currentNumberIndex = drawnNumberIndex;

          console.log(
            `Resuming: ${drawnNumber} is active. Scheduling next draw.`,
          );
          drawTimer = setTimeout(
            drawNextNumber,
            TIMING.NUMBER_VIDEO_DURATION_MS,
          );
        } else {
          console.warn("drawnNumber not found in allOutcomes during resume.");
          drawNextNumber();
        }
      } else {
        console.log("Resuming: No number active. Starting draw sequence.");
        drawNextNumber();
      }
    }

    return () => {
      if (drawTimer) {
        clearTimeout(drawTimer);
      }
    };
  }, [drawing, allOutcomes, showIntroVideo, socket]);

  function convertDateToCountDown(endTime: Date) {
    const closeTime = new Date(endTime).getTime();
    const timeRemaining = Math.max(
      -10,
      Math.round((closeTime - Date.now()) / 1000),
    );
    console.log(timeRemaining);
    setCountdown(timeRemaining);
  }

  const [minute, second] = useMemo(() => {
    if (countdown === undefined) return [0, 0];
    return [Math.floor(countdown / 60), countdown % 60];
    // return [1, 45];
  }, [countdown]);

  // Define delayed outcomes for LeftSideBar with a 1-second delay
  const [delayedOutcomes, setDelayedOutcomes] = useState<number[]>([]);

  // Effect to update delayedOutcomes with a delay when outcomes change
  useEffect(() => {
    const timer = setTimeout(() => {
      setDelayedOutcomes([...outcomes]);
    }, 500); // 200ms delay

    return () => clearTimeout(timer);
  }, [outcomes]);

  // Effect to persist game history to localStorage whenever it changes
  useEffect(() => {
    if (gameHistory.length > 0) {
      storeLocalGameHistory(gameHistory);
    }
  }, [gameHistory]);

  // Effect to update delayedDrawnNumber with a 1-second delay when drawnNumber changes
  useEffect(() => {
    const timer = setTimeout(() => {
      setDelayedDrawnNumber(drawnNumber);
    }, 1000);

    return () => clearTimeout(timer);
  }, [drawnNumber]);

  // Initialize video preloading
  useEffect(() => {
    const initializePreloading = async () => {
      // Preload images first (quick)
      [
        draw9Img,
        betsClosedImg,
        appLogoImg,
        placeHolderImg,
        introvideoImg,
      ].forEach((src) => {
        new Image().src = src;
      });

      // Preload number images
      Object.values(numberImageModules).forEach((img) => {
        new Image().src = img as string;
      });

      // Intelligent video preloading strategy
      // Phase 1: Critical videos (intro + most likely numbers)
      const criticalVideos: string[] = [introVideo];

      // Phase 2: High probability numbers (middle range numbers are more likely)
      const highPriorityVideos: string[] = [];

      // Phase 3: All remaining videos
      const normalPriorityVideos: string[] = [];

      // Distribute videos based on probability
      // Numbers 20-60 are more likely to be drawn in keno
      for (let i = 1; i <= 80; i++) {
        const videoUrl = getOutcomeVideoUrl(i);
        if (videoUrl) {
          if (i >= 0 && i <= 80) {
            highPriorityVideos.push(videoUrl);
          } else {
            normalPriorityVideos.push(videoUrl);
          }
        }
      }

      // Phase 1: Load critical videos first (intro + first few numbers)
      console.log("🚀 Starting Phase 1: Critical videos");
      await preloadVideos(criticalVideos, {
        priority: "high",
        retryAttempts: 3,
        onProgress: (status) => {
          console.log(`📹 Phase 1 progress: ${status.loaded}/${status.total}`);
        },
      });

      // Phase 2: Load high probability videos
      console.log("🎯 Starting Phase 2: High probability videos");
      await preloadVideos(highPriorityVideos, {
        priority: "high",
        retryAttempts: 2,
        onProgress: (status) => {
          console.log(`📹 Phase 2 progress: ${status.loaded}/${status.total}`);
        },
      });

      // Phase 3: Load remaining videos in background
      console.log("🔄 Starting Phase 3: Remaining videos");
      await preloadVideos(normalPriorityVideos, {
        priority: "normal",
        retryAttempts: 1,
        onProgress: (status) => {
          console.log(`📹 Phase 3 progress: ${status.loaded}/${status.total}`);
        },
        onComplete: () => {
          console.log("✅ All video preloading phases complete!");
        },
        onError: (error) => {
          console.warn("⚠️ Video preloading error:", error);
        },
      });
    };

    initializePreloading();
  }, [preloadVideos]);

  const hourNumber = (num: string | number) => {
    return (
      <img
        crossOrigin="anonymous"
        className="HourNumberImage"
        src={getNumberImageUrl(num)}
        alt={`Number ${num}`}
      />
    );
  };
  const MultipleLevels = () => (
    <div style={{ fontSize: "4.2vw" }} className="mt-5% text-center">
      <div className="text-error Goodtimes">Pick 3</div>
      <div className="Goodtimes -my-[2vh]">to</div>
      <div className="text-error Goodtimes -py-[2vh]">Pick 10</div>
      <div
        className="-py-[2vh] -my-[2vh] font-semibold"
        style={{ fontSize: "4vw" }}
      >
        games have
      </div>
      <div
        className="Goodtimes -mb-[3vh] tracking-widest text-[#f3f300]"
        style={{ fontSize: "4vw" }}
      >
        Multiple
      </div>
      <span
        className="Goodtimes -mb-[3vh] tracking-widest text-[#f3f300]"
        style={{ fontSize: "4vw" }}
      >
        Pay Levels
      </span>
      <div className="" style={{ fontSize: "4vw" }}>
        on other sports.
      </div>
    </div>
  );
  const TwentyBalls = () => (
    <div
      style={{ fontSize: "4.5vw" }}
      className="Goodtimes pt-[8vh] text-center leading-[14vh] tracking-wider"
    >
      <span className="text-error">20</span>{" "}
      <span className="pr-[0.5vh]">b</span>alls
      <br />
      <span className="pr-[0.5vh]">dra</span>
      <span>wn</span>
      <br />
      from <span className="text-error">80</span>
    </div>
  );
  const PickLevels = () => (
    <div
      className="prize-table-container"
      style={{
        width: "100%",
        marginTop: "1vh",
      }}
    >
      <div
        style={{
          textAlign: "center",
          fontSize: "4.3vw",
        }}
        className="Goodtimes text-error"
      >
        PICK {pickLevel}
      </div>
      <div
        style={{
          fontSize: "4vw",
        }}
        className="mx-[15%] grid grid-cols-2"
      >
        <div style={{ color: "#f3f300", fontWeight: "bold" }}>HITS</div>
        <div
          style={{
            color: "#f3f300",
            fontWeight: "bold",
            // minWidth: "150px",
          }}
        >
          WIN
        </div>
      </div>
      {displayablePrizes.map((item) => {
        return (
          <div
            key={item.hits}
            style={{
              color: "white",
              fontSize: "2.7vw",
              // gap: "",
            }}
            className="mx-[15%] grid grid-cols-2 leading-[5.5vh] md:leading-[7vh]"
          >
            <div>{item.hits}</div>
            <div>{item.win.toLocaleString()}</div>
          </div>
        );
      })}
    </div>
  );

  const PickTen = () => (
    <div style={{ fontSize: "4vw" }} className="pt-16 text-center">
      <p className="leading-[5vh]">Play</p>
      <p>
        The <span className="text-error Goodtimes">PICK 10</span> Game{" "}
      </p>
      <div className="pt-[4vh] leading-[7vh]">
        <p>
          Get <span className="text-error">10</span> numbers <br /> correct,
          and{" "}
        </p>
        <p>win the</p>
      </div>
      <p className="pt-[6vh]">
        <span className="text-error Goodtimes">PICK 10</span> JACKPOT
      </p>
    </div>
  );

  const OneToTen = () => (
    <div
      style={{ fontSize: "4.5vw" }}
      className="Goodtimes pt-[8vh] text-center leading-[14vh] tracking-wider"
    >
      pick <span className="text-error">1</span> to{" "}
      <span className="text-error">10</span> <br /> numbers <br /> from{" "}
      <span className="text-error">80</span>
    </div>
  );

  return (
    <>
      <div className="App">
        {!drawing && countdown! > 10 && !showIntroVideo && !showGameHistory && (
          <audio loop ref={introAudioRef} src={introSound} />
        )}
        {countdown && countdown <= 10 ? (
          <audio ref={countdownAudio} loop src={countdownSound} />
        ) : null}
        {!showIntroVideo && !drawing && !showGameHistory && (
          <div className="NumberAppContainer">
            <div className="leftSideBar h-[100%]">
              <LeftSideBar
                type="static"
                outcomes={outcomes}
                roundNumber={eventNumber}
              />
            </div>
            <div className="info flex flex-col items-center">
              <div className="text-darkOrange Eurostib flex items-center justify-between uppercase">
                <div>
                  <img
                    crossOrigin="anonymous"
                    className="drawTextImage"
                    src={draw9Img}
                    alt="Draw text"
                  />
                </div>
                <p
                  style={{
                    fontSize: "7.6vh",
                    fontFamily: "Eurostib",
                    lineHeight: 1,
                    fontWeight: "bolder",
                  }}
                  className="text-shadow text-white"
                >
                  {isServerDown
                    ? offlineroundNumber
                    : gameStatus?.eventNumber || undefined}
                </p>
              </div>
              <div
                className={`mt-10 flex ${
                  countdown !== undefined &&
                  countdown < 10 &&
                  gameStatus?.status !== "BETTING_CLOSED"
                    ? "blink transition-opacity"
                    : "opacity-100"
                }`}
              >
                {minute !== undefined && second !== undefined && (
                  <span className="flex h-[5.6vw]">
                    {gameStatus?.status === "BETTING_CLOSED" ? (
                      <img
                        crossOrigin="anonymous"
                        src={betsClosedImg}
                        style={{ height: "4vw" }}
                        alt="Bets closed"
                      />
                    ) : (
                      <>
                        {minute < 10 ? (
                          <>
                            {hourNumber(0)} {hourNumber(minute)}
                          </>
                        ) : (
                          <>
                            {hourNumber(String(minute).split("")[0])}{" "}
                            {hourNumber(String(minute).split("")[1])}
                          </>
                        )}
                        {hourNumber("9colon")}
                        {second < 10 ? (
                          <>
                            {hourNumber(0)}
                            {hourNumber(second)}
                          </>
                        ) : (
                          <>
                            {hourNumber(String(second).split("")[0])}{" "}
                            {hourNumber(String(second).split("")[1])}
                          </>
                        )}
                      </>
                    )}
                  </span>
                )}
              </div>
              {((minute === 3 && second > 56) || minute === 4) && (
                // <TwentyBalls />
                <MultipleLevels />
              )}
              {minute === 3 && second > 52 && second <= 56 && <PickTen />}
              {minute === 3 && second > 48 && second <= 52 && (
                <MultipleLevels />
              )}
              {minute === 3 && second > 42 && second <= 48 && <OneToTen />}
              {minute === 3 && second > 38 && second <= 42 && <TwentyBalls />}
              {minute === 3 && second > 34 && second <= 38 && <PickTen />}
              {minute === 3 && second > 30 && second <= 34 && (
                <MultipleLevels />
              )}
              {minute === 3 && second >= 25 && second <= 30 && <TwentyBalls />}
              {minute === 2 && second > 36 && second <= 42 && <TwentyBalls />}
              {minute === 2 && second > 31 && second <= 36 && <PickTen />}
              {minute === 2 && second >= 27 && second <= 31 && (
                <MultipleLevels />
              )}
              {(minute === 2 && second > 42) ||
              (minute === 3 && second < 25) ? (
                <PickLevels />
              ) : null}
              {(minute === 2 && second < 27) ||
              (minute === 1 && second > 52) ? (
                <PickLevels />
              ) : null}
              {(minute === 1 && second <= 34) ||
              (minute === 0 && second > 59) ? (
                <PickLevels />
              ) : null}
              {minute === 0 && second <= 40 && second > 6 ? (
                <PickLevels />
              ) : null}
              {minute === 1 && second <= 52 && second > 47 && <OneToTen />}
              {minute === 1 && second <= 47 && second > 43 && <TwentyBalls />}
              {minute === 1 && second <= 43 && second > 38 && <PickTen />}
              {minute === 1 && second <= 38 && second > 34 && (
                <MultipleLevels />
              )}
              {minute === 0 && second <= 59 && second > 54 && <OneToTen />}
              {minute === 0 && second <= 54 && second > 49 && <TwentyBalls />}
              {minute === 0 && second <= 49 && second > 44 && <PickTen />}
              {minute === 0 && second <= 44 && second > 40 && (
                <MultipleLevels />
              )}
              {minute === 0 && second <= 6 && second > 1 && <OneToTen />}
              {minute === 0 && second <= 1 && second > -3 && <TwentyBalls />}
              {minute === 0 && second <= -3 && <PickTen />}
            </div>
          </div>
        )}
        {!showIntroVideo && !showGameHistory && drawing && (
          <div className="NumberAppContainer">
            <div className="leftSideBar h-[100%]">
              <LeftSideBar
                type={"drawing"}
                outcomes={delayedOutcomes}
                roundNumber={eventNumber}
              />
            </div>
            <div className="drawingVideoContainer">
              <div className="drawenNumbers text-shadow z-50 text-[4.2vw]">
                <sup>{outcomes.length}</sup>
                <span className="text-gray-300/80">/</span>
                <sub>{allOutcomes.length}</sub>
              </div>
              {delayedDrawnNumber ? (
                <>
                  <video
                    poster={placeHolderImg}
                    key="number-video"
                    ref={videoRef}
                    src={getOutcomeVideoUrl(delayedDrawnNumber)}
                    playsInline
                    preload="auto"
                    className="absolute inset-0 h-full w-full border-none object-cover outline-none"
                    style={{ opacity: 1 }}
                  />
                  {/* Show loading indicator if video is not ready */}
                  {!isVideoReady(
                    getOutcomeVideoUrl(delayedDrawnNumber) || "",
                  ) && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/50">
                      <img
                        src={placeHolderImg}
                        alt="Placeholder"
                        className="size-full object-cover"
                      />
                    </div>
                  )}
                </>
              ) : (
                <img
                  src={placeHolderImg}
                  alt="placeholder"
                  className="absolute inset-0 h-full w-full border-none object-cover outline-none"
                />
              )}
            </div>
          </div>
        )}
        {/* {showGameHistory && ( */}
        {!showIntroVideo && !drawing && showGameHistory && (
          <div className="history Eurostib px-[3vh] pt-[2.7vw]">
            {gameHistory.map((game) => (
              <div
                key={game._id}
                className="history-entry"
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "1vw",
                  width: "100%",
                }}
              >
                <p
                  style={{
                    fontSize: "2.9vw",
                    fontWeight: "bold",
                    color: "white",
                  }}
                >
                  {game.eventNumber}
                </p>
                <div className="middle-line grid grid-cols-20 gap-[0.1vw]">
                  {game.drawnNumbers
                    .sort((a, b) => a - b)
                    .map((outcome, i) => (
                      <div className="flex">
                        <div
                          key={outcome}
                          style={{
                            backgroundColor:
                              outcome > 40 ? "#fe940d" : "#fef109",
                            color: "black",
                          }}
                          className="grid size-[3.9vw] place-items-center rounded-full text-[2.3vw] font-bold"
                        >
                          {outcome}
                        </div>
                        {i === 9 ? (
                          <div className="w-[0.5vh] place-items-center bg-red-500 opacity-50"></div>
                        ) : null}
                      </div>
                    ))}
                </div>
              </div>
            ))}
          </div>
        )}
        {/* <div className="mt-[4vh] flex flex-col">
              <div className="flex w-full items-center justify-between">
                <div className="flex items-center">
                  <div>
                    <img
                      crossOrigin="anonymous"
                      className="drawTextImage"
                      src={draw9Img}
                      alt="draw text"
                    />
                  </div>
                  <p
                    style={{
                      fontSize: "7.5vh",
                      fontFamily: "Eurostib",
                      lineHeight: 1,
                      fontWeight: 900,
                    }}
                    className="text-shadow text-white"
                  >
                    {roundNumber}
                  </p>
                </div> */}
        {/* padding: 0.2vw 1.2vw 0 3.5vw; */}
        {showIntroVideo && (
          <div className="relative">
            <div className="absolute top-[4.4vh] left-[3.5vw] flex flex-row items-center uppercase">
              <img
                crossOrigin="anonymous"
                className="drawTextImage"
                src={draw9Img}
                alt="draw text"
              />
              <p
                style={{
                  fontSize: "7.6vh",
                  fontFamily: "Eurostib",
                  lineHeight: 1,
                  fontWeight: 900,
                }}
                className="text-shadow text-white"
              >
                {eventNumber}
              </p>
            </div>
            <video
              key="intro-video"
              ref={introVideoRef}
              poster={introvideoImg}
              src={introVideo}
              preload="auto"
              onEnded={() => {
                setShowIntroVideo(false);
                setDrawing(true);
                if (
                  allOutcomes.length === 20 &&
                  outcomes.length === 0 &&
                  drawnNumber === undefined
                ) {
                  setTimeout(() => {
                    setDrawnNumber(allOutcomes[0]);
                  }, 2000);
                  setTimeout(() => {
                    setOutcomes([allOutcomes[0]]);
                  }, 3000);
                }
              }}
              className="size-full object-cover"
            />
            {/* Show loading indicator if intro video is not ready */}
            {!isVideoReady(introVideo) && (
              <div className="absolute inset-0 flex items-center justify-center">
                <img
                  src={introvideoImg}
                  alt="intro video image"
                  className="size-full"
                />
              </div>
            )}
            <img
              crossOrigin="anonymous"
              src={appLogoImg}
              alt="app logo"
              //  0.2vw 1.2vw 0 3.5vw
              // padding: 0.2vw 1.2vw 0 3.5vw;
              className="absolute bottom-[3.9vh] left-[3.5vw] z-50 h-[9.5vh] w-auto object-contain"
            />
          </div>
        )}
      </div>
    </>
  );
}
