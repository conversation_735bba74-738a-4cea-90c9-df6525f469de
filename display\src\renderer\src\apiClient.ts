import axios from "axios";
import localstore from "./utils/localstore";

const BASE_URL = `${import.meta.env.VITE_BACKEND_API_URL}`;

export const apiClient = axios.create({
  baseURL: BASE_URL,
});

// Add a request interceptor to include the access token in the headers
apiClient.interceptors.request.use((config) => {
  const access_token = localstore.getToken();
  console.log("Access token: ", access_token)
  if (access_token) {
    config.headers["Authorization"] = `Bearer ${access_token}`;
  }
  return config;
});

