"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
const electron = require("electron");
const path = require("path");
const utils = require("@electron-toolkit/utils");
const icon = path.join(__dirname, "../../resources/icon.png");
let authStore = null;
const initializeStore = async () => {
  try {
    const ElectronStore = await import("electron-store");
    const StoreClass = ElectronStore.default;
    authStore = new StoreClass({
      name: "auth",
      watch: true
    });
  } catch (error) {
    console.error("❌ Failed to initialize Electron Store:", error);
  }
};
function createWindow() {
  const mainWindow = new electron.BrowserWindow({
    show: false,
    fullscreen: true,
    autoHideMenuBar: true,
    kiosk: true,
    // Kiosk mode - prevents exiting fullscreen
    alwaysOnTop: true,
    // Keep window always on top
    skipTaskbar: true,
    // Hide from taskbar
    frame: false,
    // Remove window frame completely
    resizable: false,
    // Prevent resizing
    minimizable: false,
    // Prevent minimizing
    maximizable: false,
    // Prevent maximizing
    closable: true,
    // Allow closing with Ctrl+Q
    ...process.platform === "linux" ? { icon } : {},
    webPreferences: {
      preload: path.join(__dirname, "../preload/index.js"),
      sandbox: false,
      webSecurity: true,
      devTools: false
      // Completely disable developer tools
    }
  });
  mainWindow.on("ready-to-show", () => {
    mainWindow.show();
    mainWindow.setFullScreen(true);
    mainWindow.setMenuBarVisibility(false);
    electron.Menu.setApplicationMenu(null);
  });
  mainWindow.on("leave-full-screen", () => {
    mainWindow.setFullScreen(true);
  });
  mainWindow.on("minimize", () => {
    mainWindow.restore();
  });
  mainWindow.webContents.on("before-input-event", (event, input) => {
    if (input.key === "F12") {
      event.preventDefault();
      return;
    }
    if (input.key === "F11") {
      event.preventDefault();
      return;
    }
    if (input.key === "F12") {
      event.preventDefault();
      return;
    }
    if (input.control && input.shift && input.key === "I" || input.key === "F12" && input.meta) {
      event.preventDefault();
      return;
    }
    if (input.key === "Alt") {
      event.preventDefault();
      return;
    }
    if (input.control && input.key === "w") {
      event.preventDefault();
      return;
    }
    if (input.control && input.key === "q") {
      event.preventDefault();
      return;
    }
    if (input.control && input.key === "r") {
      event.preventDefault();
      return;
    }
    if (input.key === "Escape") {
      event.preventDefault();
      return;
    }
    if (input.key === "Meta") {
      event.preventDefault();
      return;
    }
    if (input.control && input.alt) {
      event.preventDefault();
      return;
    }
  });
  mainWindow.webContents.on("context-menu", (event) => {
    event.preventDefault();
  });
  mainWindow.webContents.on("devtools-opened", () => {
    mainWindow.webContents.closeDevTools();
  });
  mainWindow.webContents.session.setPermissionRequestHandler(() => {
    return false;
  });
  mainWindow.webContents.session.webRequest.onHeadersReceived(
    (details, callback) => {
      callback({
        responseHeaders: {
          ...details.responseHeaders,
          "Content-Security-Policy": [
            "default-src 'self' 'unsafe-inline' 'unsafe-eval' embedded-asset: data: blob:; img-src 'self' 'unsafe-inline' embedded-asset: data: blob:; media-src 'self' 'unsafe-inline' embedded-asset: data: blob:; font-src 'self' 'unsafe-inline' embedded-asset: data: blob:; style-src 'self' 'unsafe-inline' embedded-asset: data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval' embedded-asset: data: blob:; connect-src 'self' ws: wss: http: https: embedded-asset: data: blob:;"
          ]
        }
      });
    }
  );
  mainWindow.webContents.setWindowOpenHandler((details) => {
    electron.shell.openExternal(details.url);
    return { action: "deny" };
  });
  if (utils.is.dev && process.env["ELECTRON_RENDERER_URL"]) {
    mainWindow.loadURL(process.env["ELECTRON_RENDERER_URL"]);
  } else {
    mainWindow.loadFile(path.join(__dirname, "../renderer/index.html"));
  }
}
electron.app.whenReady().then(async () => {
  utils.electronApp.setAppUserModelId("com.electron");
  await initializeStore();
  electron.ipcMain.handle("auth-store-set", (_event, key, value) => {
    if (!authStore) {
      throw new Error("Auth store not initialized");
    }
    return authStore.set(key, value);
  });
  electron.ipcMain.handle("auth-store-get", (_event, key) => {
    if (!authStore) {
      throw new Error("Auth store not initialized");
    }
    return authStore.get(key);
  });
  electron.ipcMain.handle("auth-store-has", (_event, key) => {
    if (!authStore) {
      throw new Error("Auth store not initialized");
    }
    return authStore.has(key);
  });
  electron.ipcMain.handle("auth-store-delete", (_event, key) => {
    if (!authStore) {
      throw new Error("Auth store not initialized");
    }
    return authStore.delete(key);
  });
  electron.app.on("browser-window-created", (_, window) => {
    utils.optimizer.watchWindowShortcuts(window);
  });
  electron.ipcMain.on("ping", () => console.log("pong"));
  createWindow();
  electron.app.on("activate", function() {
    if (electron.BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});
electron.app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    electron.app.quit();
  }
});
