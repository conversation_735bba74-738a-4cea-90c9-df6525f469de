import { useMemo, useState, useEffect } from "react";
import { motion } from "motion/react";

import { cn } from "@renderer/lib/utils";

const plateVariants = {
  visible: {
    opacity: 1,
  },
};

export const NumberPlate2 = ({ outcomes }: { outcomes: number[] }) => {
  const lastNumber = useMemo(() => outcomes[outcomes.length - 1], [outcomes]);
  const outcomeSet = useMemo(() => new Set(outcomes || []), [outcomes]);
  const rows = 8;
  const cols = 10;
  const grid: React.ReactNode[] = [];

  for (let row = 0; row < rows; row++) {
    for (let col = 0; col < cols; col++) {
      const number = row * cols + col + 1;
      grid.push(
        <div
          key={`${row}-${col}`}
          style={{ overflow: "visible", position: "relative" }}
        >
          <Number
            num={number}
            active={outcomeSet.has(number)}
            isLastNumber={number === lastNumber}
          />
        </div>,
      );
    }
  }

  return (
    <motion.div
      variants={plateVariants}
      initial="visible"
      animate="visible"
      className="relative grid grid-cols-10 gap-1 py-[1vh] pl-[0.5vw]"
      style={{
        overflow: "visible",
      }}
    >
      {grid}
    </motion.div>
  );
};

export const Number = ({
  num,
  active,
  isLastNumber,
}: {
  num: number;
  active: boolean;
  isLastNumber?: boolean;
}) => {
  const [showActiveStyle, setShowActiveStyle] = useState(false);

  useEffect(() => {
    if (!active) {
      setShowActiveStyle(false);
    }
  }, [active]);

  const numberClass = useMemo(() => {
    if (!active) {
      return "bg-bgRedDark";
    }

    if (showActiveStyle) {
      const baseClass = num > 40 ? "tail-active" : "head-active";

      return baseClass;
    }

    return "bg-bgRedDark";
  }, [active, num, showActiveStyle, isLastNumber]);

  // Define animation styles separately for active and inactive states
  const activeAnimationStyles = {
    // Scale animation for bounce effect
    scale: [
      1, // Start at normal scale
      1.8, // Scale up (start of bounce)
      1.1, // Maximum compression at impact
      1.2, // Maximum height of first bounce
      1.05, // Second compression at impact
      1.1, // Second bounce peak
      1, // Return to normal scale
    ],
    opacity: [
      1, // Start fully opaque
      0.5, // Scale up point
      1, // First impact
      1, // First impact
      1, // First impact
      1, // First impact
      1, // First impact
    ],
    // Use a very high z-index to ensure it appears above all other UI elements
    zIndex: 9999,
  };

  const inactiveAnimationStyles = {
    // Non-called numbers remain at normal scale and fully visible
    scale: 1,
    opacity: 1,
    zIndex: 50,
  };

  return (
    <motion.div
      className={cn(
        "flex place-content-center place-items-center items-start justify-center self-center rounded-[2vh] object-center",
        numberClass,
        active && "animated-number",
      )}
      style={{
        width: "100%",
        height: "100%",
        fontSize: "3.3vw",
        overflow: "visible",
        textAlign: "center",
        // Use relative positioning by default, will be overridden during animation
        position: active ? "absolute" : "relative",
        // When active, ensure the element takes up the full space of its container
        top: active ? "0" : undefined,
        left: active ? "0" : undefined,
        right: active ? "0" : undefined,
        bottom: active ? "0" : undefined,
        // Add a transform-origin to control the scaling center point
        transformOrigin: "center center",
      }}
      // All numbers start fully visible at normal scale
      initial={{
        scale: 1,
        opacity: 1,
        zIndex: 50,
      }}
      // Animation only applies to called/drawn numbers
      animate={active ? activeAnimationStyles : inactiveAnimationStyles}
      transition={
        active
          ? {
              delay: num * 0.0085, // Delay based on number value
              duration: 0.8, // Animation duration
              times: [
                0, // Start
                0.001, // Scale up point
                0.25, // First impact
                0.45, // First bounce peak
                0.55, // Second impact
                0.65, // Second bounce peak
                0.8, // End
              ],

              ease: "easeInOut",
              // Update animation state at the right time during animation
              // We'll set the active style to show when the number starts to become visible again
              // This corresponds to around the 25% mark in the animation (first impact)
              onUpdate: (latest: { opacity: number; scale: number }) => {
                // Check if we're past the initial scale up and starting to become visible
                if (active && latest.opacity > 0.2 && !showActiveStyle) {
                  setShowActiveStyle(true);
                }
              },
              onComplete: () => {
                console.log(`Animation completed for number ${num}`);
                // Ensure active style is applied when animation completes
                if (active) {
                  setShowActiveStyle(true);
                }
              },
            }
          : {
              // No delay for non-called numbers - they're visible immediately
              duration: 0,
            }
      }
    >
      {/* <span la> */}
      {num}
      {/* </span> */}
    </motion.div>
  );
};
