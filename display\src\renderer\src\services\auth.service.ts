import { apiClient } from "@renderer/apiClient";
import { z } from "zod";

export const loginSchema = z.object({
  username: z.string(),
  password: z.string().min(4),
});

export default {
  getAuthInfo: async () => {
    const response = await apiClient.get(`/auth`);
    return response.data;
  },

  healthCheck: async () => {
    const response = await apiClient.get(
      `https://api.playbetman1.com/v1/health-check`,
    );
    return response.data;
  },

  login: async (data: z.infer<typeof loginSchema>) => {
    const response = await apiClient.post(`/auth/login`, data);
    return response.data;
  },
};
