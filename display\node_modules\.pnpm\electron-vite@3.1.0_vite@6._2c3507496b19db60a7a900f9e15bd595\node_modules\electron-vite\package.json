{"name": "electron-vite", "version": "3.1.0", "description": "Electron build tooling based on Vite", "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./node": {"types": "./node.d.ts"}, "./package.json": "./package.json"}, "bin": {"electron-vite": "bin/electron-vite.js"}, "files": ["bin", "dist", "node.d.ts"], "engines": {"node": "^18.0.0 || >=20.0.0"}, "packageManager": "pnpm@8.6.10", "author": "<PERSON><https://github.com/alex8088>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alex8088/electron-vite.git"}, "bugs": {"url": "https://github.com/alex8088/electron-vite/issues"}, "homepage": "https://electron-vite.org", "keywords": ["electron", "vite", "cli", "plugin"], "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck": "tsc --noEmit", "build": "pnpm run lint && rollup -c rollup.config.ts --configPlugin typescript"}, "simple-git-hooks": {"pre-commit": "npx lint-staged", "commit-msg": "node scripts/verifyCommit.js $1"}, "lint-staged": {"*.js": ["prettier --write"], "*.ts?(x)": ["eslint", "prettier --parser=typescript --write"]}, "peerDependencies": {"@swc/core": "^1.0.0", "vite": "^4.0.0 || ^5.0.0 || ^6.0.0"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}}, "devDependencies": {"@eslint/js": "^9.22.0", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-typescript": "^12.1.2", "@swc/core": "^1.11.9", "@types/node": "^22.13.10", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "globals": "^16.0.0", "lint-staged": "^15.4.3", "prettier": "^3.5.3", "rollup": "^4.35.0", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-rm": "^1.0.2", "simple-git-hooks": "^2.11.1", "tslib": "^2.8.1", "typescript": "^5.7.3", "typescript-eslint": "^8.26.1", "vite": "^6.2.1"}, "dependencies": {"@babel/core": "^7.26.10", "@babel/plugin-transform-arrow-functions": "^7.25.9", "cac": "^6.7.14", "esbuild": "^0.25.1", "magic-string": "^0.30.17", "picocolors": "^1.1.1"}}