import { create } from "zustand";
import { devtools } from "zustand/middleware";

import authService from "../services/auth.service";
import { authSchema, TAuthUser } from "../types";

import localstore from "../utils/localstore";

interface AuthState {
  user?: TAuthUser;
  token: string | null;
  isAuthenticated: boolean;
  actions: {
    authenticate: (user: TAuthUser, token: string) => void;
    setAuthState: (user?: TAuthUser) => void;
    initializeAuth: () => Promise<void>;
    logout: () => void;
  };
}

export const useAuthStore = create<AuthState>()(
  devtools((set, get) => ({
    user: undefined,
    token: localstore.getToken(),
    isAuthenticated: false,

    actions: {
      authenticate: (user, token) => {
        localstore.authenticateUser(token);
        set({ user, token, isAuthenticated: true });
      },
      logout: () => {
        localstore.deauthenticateUser();
        set({ user: undefined, token: null, isAuthenticated: false });
      },
      setAuthState: (user) => {
        set({ user, isAuthenticated: !!user });
      },
      initializeAuth: async () => {
        try {
          const response = await authService.getAuthInfo();
          const safeData = authSchema.safeParse(response);

          if (safeData.success) {
            get().actions.setAuthState(safeData.data);
          }
          console.log("Alual  ");
        } catch (error) {
          get().actions.setAuthState(undefined);
          console.log("got to here");
          // Re-throw the error with a specific type so the router can handle the redirect
          return;
        }
      },
    },
  })),
);

export const useAuthActions = () => useAuthStore((state) => state.actions);
export const useAuthUser = () => useAuthStore((state) => state.user);
export const useToken = () => useAuthStore((state) => state.token);
