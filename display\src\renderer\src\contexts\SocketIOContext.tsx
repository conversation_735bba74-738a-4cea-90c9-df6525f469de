import { createContext, useContext, useEffect, useState } from "react";
import { redirect } from "@tanstack/react-router";
import { io, type Socket } from "socket.io-client";

import localstore from "@renderer/utils/localstore";

interface SocketContextType {
  socket: Socket | null;
  isServerDown: boolean;
}

const SocketIOContext = createContext<SocketContextType>({
  socket: null,
  isServerDown: false,
});

export const useSocketIO = () => useContext(SocketIOContext);

export const SocketIOProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const token = localstore.getToken();

  const [socket, setSocket] = useState<Socket | null>(null);
  const [isServerDown, setIsServerDown] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  useEffect(() => {
    if (!token) {
      if (socket) {
        socket.disconnect();
        setSocket(null);
        setIsServerDown(true);
      }
      return;
    }

    const backendUrl = "https://api.playbetman1.com";

    const socketInstance = io(backendUrl, {
      transports: ["websocket"],
      reconnection: true,
      autoConnect: true,
      reconnectionAttempts: Infinity,
      reconnectionDelayMax: 1000,
      reconnectionDelay: 1000,
      auth: (cb) => {
        cb({ token });
      },
    });

    socketInstance.on("connect", () => {
      setIsServerDown(false);
      setIsInitialLoad(false);
      socketInstance.emit("getEventDetails");
    });

    socketInstance.on("disconnect", () => {
      setIsServerDown(true);
    });

    // Handle game data events
    const handleGameData = (_data: any) => {
      setIsServerDown(false);
    };

    socketInstance.on("gameStatus", (data) => {
      handleGameData(data);
    });

    socketInstance.on("drawnNumbers", (data) => {
      handleGameData(data);
    });

    setSocket(socketInstance);

    return () => {
      socketInstance.off("connect");
      socketInstance.off("disconnect");
      socketInstance.off("gameStatus");
      socketInstance.off("drawnNumbers");

      socketInstance.disconnect();
      setSocket((prev) => (prev === socketInstance ? null : prev));
    };
  }, [token]);

  useEffect(() => {
    if (isInitialLoad && isServerDown) {
      redirect({ to: "/network-reconnect", replace: true });
    }
  }, [isInitialLoad, isServerDown]);

  return (
    <SocketIOContext.Provider
      value={{
        socket,
        isServerDown,
      }}
    >
      {children}
    </SocketIOContext.Provider>
  );
};
